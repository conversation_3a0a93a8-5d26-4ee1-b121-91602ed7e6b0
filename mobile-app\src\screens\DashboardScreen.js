import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';
import * as SecureStore from 'expo-secure-store';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { ModernCard, StatCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { StatusChip } from '../components/ModernChip';
import { StatCardSkeleton, ListSkeleton, HeaderSkeleton } from '../components/ModernLoading';
import { theme } from '../theme/theme';



export default function DashboardScreen({ navigation }) {
  const [stats, setStats] = useState({});
  const [recentProjects, setRecentProjects] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { user, logout } = useAuth();

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        // Try to get token from secure storage and set it
        try {
          const storedToken = await SecureStore.getItemAsync('authToken');
          if (storedToken) {
            console.log('🔑 Restoring token from secure storage');
            apiService.setAuthToken(storedToken);
          }
        } catch (error) {
          console.error('Failed to restore token:', error);
        }
      }

      // Load dashboard statistics with caching
      const dashboardResponse = await apiService.getDashboardStatsWithCache();
      console.log('📊 Dashboard response:', dashboardResponse);

      if (dashboardResponse.fromCache) {
        console.log('📦 Dashboard data loaded from cache');
      }

      if (dashboardResponse.success) {
        const data = dashboardResponse.data;

        // Set statistics
        setStats({
          totalProjects: data.projects.total,
          completedProjects: data.projects.completed,
          ongoingProjects: data.projects.ongoing,
          plannedProjects: data.projects.planned,
          newMessages: data.messages.new,
          totalMessages: data.messages.total,
          totalServices: data.services.total,
          totalMedia: data.media.total,
        });

        // Set recent data
        setRecentProjects(data.recent_projects || []);
        setRecentMessages(data.recent_messages || []);

        console.log('✅ Dashboard data loaded successfully');
      }
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });

      // Fallback to individual API calls with caching if dashboard endpoint fails
      try {
        console.log('🔄 Trying fallback API calls with cache...');
        const [projectsResponse, messagesResponse] = await Promise.all([
          apiService.getProjectsWithCache({ limit: 5 }),
          apiService.getMessagesWithCache({ status: 'new', limit: 5 }),
        ]);

        if (projectsResponse.success) {
          setRecentProjects(projectsResponse.data.projects);
          const projects = projectsResponse.data.projects;
          const completedCount = projects.filter(p => p.status === 'completed').length;
          const ongoingCount = projects.filter(p => p.status === 'ongoing').length;

          setStats({
            totalProjects: projectsResponse.data.pagination.total,
            completedProjects: completedCount,
            ongoingProjects: ongoingCount,
            newMessages: messagesResponse.success ? messagesResponse.data.pagination.total : 0,
          });

          console.log('✅ Fallback data loaded successfully');
        }

        if (messagesResponse.success) {
          setRecentMessages(messagesResponse.data.messages);
        }
      } catch (fallbackError) {
        console.error('❌ Fallback dashboard loading also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };



  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleLogout = async () => {
    await logout();
  };



  if (loading) {
    return (
      <View style={styles.container}>
        <HeaderSkeleton />
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <StatCardSkeleton style={styles.statCard} />
            <StatCardSkeleton style={styles.statCard} />
          </View>
          <View style={styles.statsRow}>
            <StatCardSkeleton style={styles.statCard} />
            <StatCardSkeleton style={styles.statCard} />
          </View>
        </View>
        <ListSkeleton count={3} />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.welcomeTitle}>Welcome back,</Text>
            <Text style={styles.userName}>{user?.username || 'User'}</Text>
            <Text style={styles.headerSubtitle}>
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => navigation.navigate('Profile')}
            >
              <Ionicons name="person-circle-outline" size={28} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={handleLogout}
            >
              <Ionicons name="log-out-outline" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <StatCard
            gradient={true}
            gradientColors={theme.gradients.primary}
            style={styles.statCard}
            elevation="small"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="business-outline" size={18} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.totalProjects || 0}</Text>
              <Text style={styles.statLabel}>Total Projects</Text>
            </View>
          </StatCard>

          <StatCard
            gradient={true}
            gradientColors={theme.gradients.success}
            style={styles.statCard}
            elevation="small"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="checkmark-circle-outline" size={18} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.completedProjects || 0}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
          </StatCard>
        </View>

        <View style={styles.statsRow}>
          <StatCard
            gradient={true}
            gradientColors={theme.gradients.warning}
            style={styles.statCard}
            elevation="small"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="construct-outline" size={18} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.ongoingProjects || 0}</Text>
              <Text style={styles.statLabel}>Ongoing</Text>
            </View>
          </StatCard>

          <StatCard
            gradient={true}
            gradientColors={theme.gradients.info}
            style={styles.statCard}
            elevation="small"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="mail-outline" size={18} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.newMessages || 0}</Text>
              <Text style={styles.statLabel}>New Messages</Text>
            </View>
          </StatCard>
        </View>
      </View>

      {/* Recent Projects */}
      <ModernCard style={styles.sectionCard} elevation="medium" animated={true}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Projects</Text>
          <ModernButton
            title="View All"
            variant="text"
            size="small"
            onPress={() => navigation.navigate('Projects')}
            icon="arrow-forward-outline"
            iconPosition="right"
          />
        </View>

        {recentProjects.length > 0 ? (
          recentProjects.map((project, index) => (
            <TouchableOpacity
              key={project.id}
              style={[styles.listItem, index === recentProjects.length - 1 && styles.lastListItem]}
              onPress={() => navigation.navigate('ProjectDetail', { project })}
            >
              <View style={styles.listItemLeft}>
                <View style={styles.projectIcon}>
                  <Ionicons name="business-outline" size={24} color="#3b82f6" />
                </View>
                <View style={styles.listItemContent}>
                  <Text style={styles.listItemTitle}>{project.title}</Text>
                  <Text style={styles.listItemDescription}>{project.location}</Text>
                </View>
              </View>
              <StatusChip status={project.status} />
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="business-outline" size={64} color="#94a3b8" />
            <Text style={styles.emptyText}>No recent projects</Text>
          </View>
        )}
      </ModernCard>

      {/* Recent Messages */}
      <ModernCard style={styles.sectionCard} elevation="medium" animated={true}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>New Messages</Text>
          <ModernButton
            title="View All"
            variant="text"
            size="small"
            onPress={() => navigation.navigate('Messages')}
            icon="arrow-forward-outline"
            iconPosition="right"
          />
        </View>

        {recentMessages.length > 0 ? (
          recentMessages.map((message, index) => (
            <TouchableOpacity
              key={message.id}
              style={[styles.listItem, index === recentMessages.length - 1 && styles.lastListItem]}
              onPress={() => navigation.navigate('MessageDetail', { message })}
            >
              <View style={styles.listItemLeft}>
                <View style={styles.messageIcon}>
                  <Ionicons name="mail-outline" size={24} color="#f59e0b" />
                </View>
                <View style={styles.listItemContent}>
                  <Text style={styles.listItemTitle}>{message.name}</Text>
                  <Text style={styles.listItemDescription}>
                    {message.subject || 'No subject'}
                  </Text>
                </View>
              </View>
              <Text style={styles.messageTime}>
                {new Date(message.created_at).toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="mail-outline" size={64} color="#94a3b8" />
            <Text style={styles.emptyText}>No new messages</Text>
          </View>
        )}
      </ModernCard>


    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingTop: theme.spacing.xxl + 20,
    paddingBottom: theme.spacing.xxl,
    paddingHorizontal: theme.spacing.lg,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTextContainer: {
    flex: 1,
  },
  welcomeTitle: {
    ...theme.typography.headlineMedium,
    color: 'white',
    fontWeight: '300',
    fontSize: 18,
    letterSpacing: 0.5,
  },
  userName: {
    ...theme.typography.titleLarge,
    color: 'white',
    marginTop: theme.spacing.xs,
    fontWeight: '700',
    fontSize: 24,
    letterSpacing: 0.3,
  },
  headerSubtitle: {
    ...theme.typography.bodyMedium,
    color: 'rgba(255, 255, 255, 0.85)',
    marginTop: theme.spacing.xs,
    fontSize: 14,
    fontWeight: '400',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  headerButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsContainer: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
    marginTop: -theme.spacing.xl,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    minHeight: 100,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  },
  statCardHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
  },
  statIconContainer: {
    marginBottom: theme.spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 14,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statNumber: {
    ...theme.typography.displaySmall,
    color: 'white',
    fontWeight: '800',
    marginBottom: theme.spacing.xs,
    fontSize: 24,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  statLabel: {
    ...theme.typography.labelMedium,
    color: 'rgba(255, 255, 255, 0.95)',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 11,
    letterSpacing: 0.3,
    textTransform: 'uppercase',
    lineHeight: 14,
  },
  sectionCard: {
    marginHorizontal: theme.spacing.lg,
    marginVertical: theme.spacing.md,
    borderRadius: 20,
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.04)',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
    paddingBottom: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  sectionTitle: {
    ...theme.typography.titleLarge,
    color: '#1e293b',
    fontWeight: '700',
    fontSize: 20,
    letterSpacing: 0.3,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    borderRadius: 16,
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.04,
    shadowRadius: 6,
    elevation: 2,
  },
  lastListItem: {
    marginBottom: 0,
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  listItemContent: {
    flex: 1,
    marginLeft: theme.spacing.lg,
  },
  listItemTitle: {
    ...theme.typography.bodyLarge,
    color: '#1e293b',
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
    fontSize: 16,
  },
  listItemDescription: {
    ...theme.typography.bodyMedium,
    color: '#64748b',
    fontSize: 14,
    lineHeight: 20,
  },
  projectIcon: {
    width: 48,
    height: 48,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#dbeafe',
    borderWidth: 1,
    borderColor: '#bfdbfe',
    shadowColor: '#3b82f6',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  messageIcon: {
    width: 48,
    height: 48,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    borderWidth: 1,
    borderColor: '#fde68a',
    shadowColor: '#f59e0b',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xxl,
    paddingHorizontal: theme.spacing.lg,
  },
  emptyText: {
    ...theme.typography.bodyLarge,
    color: '#64748b',
    textAlign: 'center',
    marginTop: theme.spacing.lg,
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
  },
  messageTime: {
    ...theme.typography.labelSmall,
    color: '#64748b',
    fontSize: 12,
    fontWeight: '500',
    backgroundColor: '#f1f5f9',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: 8,
  },


});
