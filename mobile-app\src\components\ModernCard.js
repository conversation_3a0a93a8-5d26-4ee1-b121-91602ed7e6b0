import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { theme } from '../theme/theme';

export const ModernCard = ({
  children,
  style,
  onPress,
  variant = 'elevated', // elevated, outlined, filled, glass
  elevation = 'medium', // xs, small, medium, large, xl, floating
  borderRadius = theme.borderRadius.lg,
  padding = theme.spacing.md,
  margin = 0,
  animated = true,
  gradient = false,
  gradientColors = theme.gradients.card,
  glassMorphism = false,
  pressScale = 0.98,
  ...props
}) => {
  const scaleValue = new Animated.Value(1);

  const handlePressIn = () => {
    if (animated && onPress) {
      Animated.spring(scaleValue, {
        toValue: pressScale,
        useNativeDriver: true,
        ...theme.animations.spring.gentle,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated && onPress) {
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
        ...theme.animations.spring.gentle,
      }).start();
    }
  };

  const getCardStyle = () => {
    const baseStyle = {
      borderRadius,
      padding,
      margin,
      overflow: 'hidden',
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surface,
          ...theme.shadows[elevation],
        };
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surface,
          borderWidth: 1,
          borderColor: theme.colors.outline,
        };
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surfaceVariant,
        };
      case 'glass':
        return {
          ...baseStyle,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)',
          ...theme.shadows.small,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: theme.colors.surface,
          ...theme.shadows[elevation],
        };
    }
  };

  const cardStyle = [getCardStyle(), style];

  const animatedStyle = animated ? {
    transform: [{ scale: scaleValue }],
  } : {};

  const CardContent = () => (
    <View style={cardStyle}>
      {children}
    </View>
  );

  const GradientCard = () => (
    <LinearGradient
      colors={gradientColors}
      style={cardStyle}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      {children}
    </LinearGradient>
  );

  if (onPress) {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          {...props}
        >
          {gradient ? <GradientCard /> : <CardContent />}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={animatedStyle}>
      {gradient ? <GradientCard /> : <CardContent />}
    </Animated.View>
  );
};

// Specialized card variants
export const ProjectCard = ({ project, onPress, style, ...props }) => (
  <ModernCard
    onPress={onPress}
    style={[styles.projectCard, style]}
    elevation="medium"
    animated={true}
    {...props}
  >
    {props.children}
  </ModernCard>
);

export const StatCard = ({ 
  icon, 
  value, 
  label, 
  color = 'primary',
  gradient = true,
  style,
  ...props 
}) => (
  <ModernCard
    gradient={gradient}
    gradientColors={theme.gradients[color] || theme.gradients.primary}
    style={[styles.statCard, style]}
    elevation="medium"
    animated={true}
    {...props}
  >
    {props.children}
  </ModernCard>
);

export const GlassCard = ({ children, style, ...props }) => (
  <ModernCard
    variant="glass"
    style={[styles.glassCard, style]}
    {...props}
  >
    {children}
  </ModernCard>
);

const styles = StyleSheet.create({
  projectCard: {
    marginBottom: theme.spacing.md,
    marginHorizontal: theme.spacing.xs,
  },
  statCard: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  glassCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
});

export default ModernCard;
